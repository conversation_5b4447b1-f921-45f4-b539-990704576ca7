using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.DTO
{
    public class UserDashboardDto
    {
        // User basic information
        public UserInfoDto UserInfo { get; set; }

        // Wallet information
        public WalletInfoDto WalletInfo { get; set; }

        // Property statistics
        public PropertyStatsDto PropertyStats { get; set; }

        // Recent transactions
        public List<WalletTransactionDto> RecentTransactions { get; set; }

        // Member ranking information
        public MemberRankingDto MemberRanking { get; set; }
    }

    public class UserInfoDto
    {
        public Guid Id { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public UserType UserType { get; set; }
        public string MemberRank { get; set; }
        public DateTimeOffset? LastLogin { get; set; }
        public DateTimeOffset CreatedAt { get; set; }
        public IEnumerable<string> Roles { get; set; }

    }

    public class WalletInfoDto
    {
        public decimal Balance { get; set; }
        public decimal TotalSpent { get; set; }
        public int TotalTransactions { get; set; }
        public decimal LastMonthSpending { get; set; }
    }

    public class PropertyStatsDto
    {
        public int TotalProperties { get; set; }
        public int ApprovedProperties { get; set; }
        public int ExpiredProperties { get; set; }
        public int DraftProperties { get; set; }
        public int RejectedByAdminProperties { get; set; }
    }

}